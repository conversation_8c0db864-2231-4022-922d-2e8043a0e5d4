import React, { useState, useRef, useEffect } from 'react';
import type { Business } from '../types';
import { getCategoryIcon } from '../lib/categoryIcons';

type AutocompleteItem = {
  type: 'business' | 'category' | 'city';
  value: string;
  label: string;
  business?: Business;
  count?: number;
};

type AutocompleteSearchProps = {
  value: string;
  onChange: (value: string) => void;
  businesses: Business[];
  placeholder?: string;
  onBusinessSelect?: (business: Business) => void;
  className?: string;
};

export function AutocompleteSearch({
  value,
  onChange,
  businesses,
  placeholder = 'Search businesses, categories, or cities...',
  onBusinessSelect,
  className = ''
}: AutocompleteSearchProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);

  // Generate autocomplete suggestions
  const suggestions: AutocompleteItem[] = React.useMemo(() => {
    if (!value || value.length < 2) return [];

    const query = value.toLowerCase();
    const items: AutocompleteItem[] = [];
    const seen = new Set<string>();

    // Business name suggestions
    businesses.forEach(business => {
      if (business.name.toLowerCase().includes(query)) {
        const key = `business-${business.name}`;
        if (!seen.has(key)) {
          seen.add(key);
          items.push({
            type: 'business',
            value: business.name,
            label: business.name,
            business
          });
        }
      }
    });

    // Category suggestions with counts
    const categoryMap = new Map<string, number>();
    businesses.forEach(business => {
      if (business.category.toLowerCase().includes(query)) {
        categoryMap.set(business.category, (categoryMap.get(business.category) || 0) + 1);
      }
    });

    categoryMap.forEach((count, category) => {
      const key = `category-${category}`;
      if (!seen.has(key)) {
        seen.add(key);
        items.push({
          type: 'category',
          value: category,
          label: category,
          count
        });
      }
    });

    // City suggestions with counts
    const cityMap = new Map<string, number>();
    businesses.forEach(business => {
      if (business.city.toLowerCase().includes(query)) {
        cityMap.set(business.city, (cityMap.get(business.city) || 0) + 1);
      }
    });

    cityMap.forEach((count, city) => {
      const key = `city-${city}`;
      if (!seen.has(key)) {
        seen.add(key);
        items.push({
          type: 'city',
          value: city,
          label: city,
          count
        });
      }
    });

    // Sort by relevance (exact matches first, then partial matches)
    return items
      .sort((a, b) => {
        const aExact = a.label.toLowerCase() === query;
        const bExact = b.label.toLowerCase() === query;
        if (aExact && !bExact) return -1;
        if (!aExact && bExact) return 1;
        
        const aStarts = a.label.toLowerCase().startsWith(query);
        const bStarts = b.label.toLowerCase().startsWith(query);
        if (aStarts && !bStarts) return -1;
        if (!aStarts && bStarts) return 1;
        
        return a.label.localeCompare(b.label);
      })
      .slice(0, 8); // Limit to 8 suggestions
  }, [value, businesses]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
    setIsOpen(newValue.length >= 2);
    setHighlightedIndex(-1);
  };

  const handleInputFocus = () => {
    if (value.length >= 2) {
      setIsOpen(true);
    }
  };

  const handleInputBlur = () => {
    // Delay to allow click on suggestions
    setTimeout(() => setIsOpen(false), 200);
  };

  const handleSuggestionClick = (suggestion: AutocompleteItem) => {
    if (suggestion.type === 'business' && suggestion.business && onBusinessSelect) {
      onBusinessSelect(suggestion.business);
    } else {
      onChange(suggestion.label);
    }
    setIsOpen(false);
    inputRef.current?.blur();
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!isOpen) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (highlightedIndex >= 0) {
          handleSuggestionClick(suggestions[highlightedIndex]);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setHighlightedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  const getSuggestionIcon = (suggestion: AutocompleteItem) => {
    switch (suggestion.type) {
      case 'business':
        if (suggestion.business) {
          const CategoryIcon = getCategoryIcon(suggestion.business.category);
          return <CategoryIcon size={16} className="text-blue-600" />;
        }
        return <span className="text-blue-600">🏢</span>;
      case 'category':
        const CategoryIcon = getCategoryIcon(suggestion.value);
        return <CategoryIcon size={16} className="text-green-600" />;
      case 'city':
        return <span className="text-purple-600">📍</span>;
      default:
        return null;
    }
  };

  const getSuggestionTypeLabel = (type: AutocompleteItem['type']) => {
    switch (type) {
      case 'business': return 'Business';
      case 'category': return 'Category';
      case 'city': return 'City';
    }
  };

  return (
    <div className={`relative ${className}`}>
      <input
        ref={inputRef}
        type="text"
        value={value}
        onChange={handleInputChange}
        onFocus={handleInputFocus}
        onBlur={handleInputBlur}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 dark:text-white"
        autoComplete="off"
      />

      {isOpen && suggestions.length > 0 && (
        <div
          ref={resultsRef}
          className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border dark:border-gray-600 rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto"
        >
          {suggestions.map((suggestion, index) => (
            <div
              key={`${suggestion.type}-${suggestion.value}`}
              className={`flex items-center gap-3 px-4 py-3 cursor-pointer transition-colors ${
                index === highlightedIndex
                  ? 'bg-blue-50 dark:bg-blue-900/20'
                  : 'hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}
              onClick={() => handleSuggestionClick(suggestion)}
            >
              <div className="flex-shrink-0">
                {getSuggestionIcon(suggestion)}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-gray-900 dark:text-white truncate">
                    {suggestion.label}
                  </span>
                  {suggestion.count && (
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      ({suggestion.count})
                    </span>
                  )}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {getSuggestionTypeLabel(suggestion.type)}
                  {suggestion.business && (
                    <>
                      {' • '}
                      <span>{suggestion.business.category} • {suggestion.business.city}</span>
                    </>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
