import { supabase } from './supabase';
import type { Business } from '../types';

// Service worker registration for push notifications
export async function registerServiceWorker(): Promise<ServiceWorkerRegistration | null> {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      });
      
      console.log('Service Worker registered:', registration);
      return registration;
    } catch (error) {
      console.error('Service Worker registration failed:', error);
      return null;
    }
  }
  return null;
}

// Request permission for push notifications
export async function requestNotificationPermission(): Promise<NotificationPermission> {
  if (!('Notification' in window)) {
    console.warn('This browser does not support notifications');
    throw new Error('Push notifications are not supported in this browser');
  }

  if (Notification.permission === 'default') {
    try {
      const permission = await Notification.requestPermission();
      return permission;
    } catch (error) {
      console.error('Failed to request notification permission:', error);
      throw new Error('Failed to request notification permission');
    }
  }

  return Notification.permission;
}

// Subscribe to push notifications
export async function subscribeToPushNotifications(): Promise<PushSubscription | null> {
  try {
    const permission = await requestNotificationPermission();
    
    if (permission !== 'granted') {
      console.log('Notification permission not granted:', permission);
      throw new Error('Notification permission was denied');
    }

    const registration = await registerServiceWorker();
    if (!registration) {
      console.error('Service worker not registered');
      throw new Error('Service worker registration failed');
    }

    // Check if already subscribed
    const existingSubscription = await registration.pushManager.getSubscription();
    if (existingSubscription) {
      await storeSubscription(existingSubscription);
      return existingSubscription;
    }

    // Create new subscription
    const vapidPublicKey = import.meta.env.VITE_VAPID_PUBLIC_KEY;
    
    let subscriptionOptions: PushSubscriptionOptions;
    
    if (vapidPublicKey && vapidPublicKey !== 'your-vapid-public-key-here') {
      subscriptionOptions = {
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(vapidPublicKey).buffer as ArrayBuffer
      };
    } else {
      console.warn('VAPID public key not configured properly');
      subscriptionOptions = {
        userVisibleOnly: true,
        applicationServerKey: new ArrayBuffer(0) // Empty key as fallback
      };
    }

    const subscription = await registration.pushManager.subscribe(subscriptionOptions);

    // Store subscription
    await storeSubscription(subscription);

    return subscription;
  } catch (error) {
    console.error('Failed to subscribe to push notifications:', error);
    throw error;
  }
}

// Store subscription in localStorage and optionally send to backend
async function storeSubscription(subscription: PushSubscription): Promise<void> {
  // Store locally
  localStorage.setItem('dfwbb:push-subscription', JSON.stringify(subscription));

  // Optionally store in Supabase for server-side notifications
  try {
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
    const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
    
    if (supabaseUrl && supabaseKey && supabaseUrl !== 'your-supabase-url-here') {
      await supabase
        .from('push_subscriptions')
        .upsert({
          endpoint: subscription.endpoint,
          keys: subscription.toJSON().keys,
          created_at: new Date().toISOString()
        });
    }
  } catch (error) {
    console.warn('Failed to store subscription in database:', error);
  }
}

// Unsubscribe from push notifications
export async function unsubscribeFromPushNotifications(): Promise<boolean> {
  const registration = await navigator.serviceWorker.getRegistration();
  if (!registration) return false;

  try {
    const subscription = await registration.pushManager.getSubscription();
    if (subscription) {
      const success = await subscription.unsubscribe();
      if (success) {
        localStorage.removeItem('dfwbb:push-subscription');
        
        // Remove from backend if stored there
        try {
          const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
          const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
          
          if (supabaseUrl && supabaseKey && supabaseUrl !== 'your-supabase-url-here') {
            await supabase
              .from('push_subscriptions')
              .delete()
              .eq('endpoint', subscription.endpoint);
          }
        } catch (error) {
          console.warn('Failed to remove subscription from database:', error);
        }
      }
      return success;
    }
  } catch (error) {
    console.error('Failed to unsubscribe:', error);
  }
  
  return false;
}

// Check if push notifications are supported and enabled
export function isPushNotificationSupported(): boolean {
  return 'serviceWorker' in navigator && 
         'PushManager' in window && 
         'Notification' in window;
}

export function isPushNotificationGranted(): boolean {
  return Notification.permission === 'granted';
}

// Show local notification (fallback for browsers without push support)
export function showLocalNotification(title: string, options?: NotificationOptions): void {
  if (isPushNotificationGranted()) {
    new Notification(title, {
      icon: '/icons/icon-192.png',
      badge: '/icons/icon-72.png',
      ...options
    });
  }
}

// Monitor for new businesses and show notifications
export function startBusinessMonitoring(): void {
  // Check for new businesses every 30 minutes
  const interval = setInterval(async () => {
    try {
      await checkForNewBusinesses();
    } catch (error) {
      console.error('Error checking for new businesses:', error);
    }
  }, 30 * 60 * 1000); // 30 minutes

  // Store interval ID for cleanup
  localStorage.setItem('dfwbb:monitoring-interval', interval.toString());
}

export function stopBusinessMonitoring(): void {
  const intervalId = localStorage.getItem('dfwbb:monitoring-interval');
  if (intervalId) {
    clearInterval(parseInt(intervalId));
    localStorage.removeItem('dfwbb:monitoring-interval');
  }
}

// Check for new businesses
async function checkForNewBusinesses(): Promise<void> {
  const lastCheck = localStorage.getItem('dfwbb:last-new-business-check');
  const lastCheckDate = lastCheck ? new Date(lastCheck) : new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago

  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
  
  if (supabaseUrl && supabaseKey && supabaseUrl !== 'your-supabase-url-here') {
    try {
      const { data, error } = await supabase
        .from('businesses')
        .select('name, category, city, created_at')
        .eq('status', 'approved')
        .gt('created_at', lastCheckDate.toISOString())
        .order('created_at', { ascending: false });

      if (!error && data && data.length > 0) {
        // Show notification for new businesses
        if (data.length === 1) {
          showLocalNotification(`New Business Added!`, {
            body: `${data[0].name} (${data[0].category}) in ${data[0].city}`,
            tag: 'new-business',
            requireInteraction: false
          });
        } else {
          showLocalNotification(`${data.length} New Businesses Added!`, {
            body: 'Check out the latest Black-owned businesses in DFW',
            tag: 'new-businesses',
            requireInteraction: false
          });
        }
      }

      localStorage.setItem('dfwbb:last-new-business-check', new Date().toISOString());
    } catch (error) {
      console.warn('Failed to check for new businesses:', error);
    }
  }
}

// Utility function to convert VAPID key
function urlBase64ToUint8Array(base64String: string): Uint8Array {
  const padding = '='.repeat((4 - base64String.length % 4) % 4);
  const base64 = (base64String + padding)
    .replace(/-/g, '+')
    .replace(/_/g, '/');

  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}
