import { useState, useEffect } from 'react';

export interface Favorite {
  businessName: string;
  addedAt: string;
}

let notificationCallback: ((type: 'add' | 'remove', businessName: string) => void) | null = null;

export function setFavoriteNotificationCallback(callback: (type: 'add' | 'remove', businessName: string) => void) {
  notificationCallback = callback;
}

export function useFavorites() {
  const [favorites, setFavorites] = useState<Favorite[]>([]);

  useEffect(() => {
    const stored = localStorage.getItem('dfw-bb-favorites');
    if (stored) {
      try {
        setFavorites(JSON.parse(stored));
      } catch (error) {
        console.error('Error parsing stored favorites:', error);
        setFavorites([]);
      }
    }
  }, []);

  const addToFavorites = (businessName: string) => {
    const newFavorite: Favorite = {
      businessName,
      addedAt: new Date().toISOString(),
    };
    
    const updatedFavorites = [...favorites, newFavorite];
    setFavorites(updatedFavorites);
    localStorage.setItem('dfw-bb-favorites', JSON.stringify(updatedFavorites));
    
    if (notificationCallback) {
      notificationCallback('add', businessName);
    }
  };

  const removeFromFavorites = (businessName: string) => {
    const updatedFavorites = favorites.filter(fav => fav.businessName !== businessName);
    setFavorites(updatedFavorites);
    localStorage.setItem('dfw-bb-favorites', JSON.stringify(updatedFavorites));
    
    if (notificationCallback) {
      notificationCallback('remove', businessName);
    }
  };

  const isFavorite = (businessName: string) => {
    return favorites.some(fav => fav.businessName === businessName);
  };

  const toggleFavorite = (businessName: string) => {
    if (isFavorite(businessName)) {
      removeFromFavorites(businessName);
    } else {
      addToFavorites(businessName);
    }
  };

  return {
    favorites,
    addToFavorites,
    removeFromFavorites,
    isFavorite,
    toggleFavorite,
  };
}
