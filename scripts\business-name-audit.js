#!/usr/bin/env node

/**
 * Business Name Audit Script
 *
 * This script performs a comprehensive audit of business names across all data sources
 * to identify inconsistencies, duplicates, and potential issues that could affect
 * search functionality and user experience.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Color codes for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m',
  bright: '\x1b[1m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  console.log('\n' + '='.repeat(60));
  log(message, 'bright');
  console.log('='.repeat(60));
}

function logSection(message) {
  console.log('\n' + '-'.repeat(40));
  log(message, 'cyan');
  console.log('-'.repeat(40));
}

// Load business data files
function loadBusinessData() {
  const basicDataPath = path.join(__dirname, '../src/data/dfw-businesses.json');
  const enhancedDataPath = path.join(__dirname, '../src/data/dfw-businesses-enhanced.json');
  
  let basicData = [];
  let enhancedData = [];
  
  try {
    if (fs.existsSync(basicDataPath)) {
      basicData = JSON.parse(fs.readFileSync(basicDataPath, 'utf8'));
      log(`✓ Loaded ${basicData.length} businesses from basic data file`, 'green');
    } else {
      log(`⚠ Basic data file not found: ${basicDataPath}`, 'yellow');
    }
  } catch (error) {
    log(`✗ Error loading basic data: ${error.message}`, 'red');
  }
  
  try {
    if (fs.existsSync(enhancedDataPath)) {
      enhancedData = JSON.parse(fs.readFileSync(enhancedDataPath, 'utf8'));
      log(`✓ Loaded ${enhancedData.length} businesses from enhanced data file`, 'green');
    } else {
      log(`⚠ Enhanced data file not found: ${enhancedDataPath}`, 'yellow');
    }
  } catch (error) {
    log(`✗ Error loading enhanced data: ${error.message}`, 'red');
  }
  
  return { basicData, enhancedData };
}

// Compare business names between datasets
function compareBusinessNames(basicData, enhancedData) {
  logSection('BUSINESS NAME COMPARISON');
  
  const basicNames = new Set(basicData.map(b => b.name));
  const enhancedNames = new Set(enhancedData.map(b => b.name));
  
  // Find businesses only in basic data
  const onlyInBasic = basicData.filter(b => !enhancedNames.has(b.name));
  if (onlyInBasic.length > 0) {
    log(`\n⚠ ${onlyInBasic.length} businesses found only in basic data:`, 'yellow');
    onlyInBasic.forEach(b => {
      log(`  • ${b.name} (${b.category}, ${b.city})`, 'yellow');
    });
  }
  
  // Find businesses only in enhanced data
  const onlyInEnhanced = enhancedData.filter(b => !basicNames.has(b.name));
  if (onlyInEnhanced.length > 0) {
    log(`\n✓ ${onlyInEnhanced.length} businesses found only in enhanced data:`, 'green');
    onlyInEnhanced.forEach(b => {
      log(`  • ${b.name} (${b.category}, ${b.city})`, 'green');
    });
  }
  
  return { onlyInBasic, onlyInEnhanced };
}

// Check for data inconsistencies between matching businesses
function checkDataInconsistencies(basicData, enhancedData) {
  logSection('DATA INCONSISTENCY CHECK');
  
  const inconsistencies = [];
  
  basicData.forEach(basicBusiness => {
    const enhancedBusiness = enhancedData.find(e => e.name === basicBusiness.name);
    if (enhancedBusiness) {
      const issues = [];
      
      // Check category consistency
      if (basicBusiness.category !== enhancedBusiness.category) {
        issues.push(`Category: "${basicBusiness.category}" vs "${enhancedBusiness.category}"`);
      }
      
      // Check city consistency
      if (basicBusiness.city !== enhancedBusiness.city) {
        issues.push(`City: "${basicBusiness.city}" vs "${enhancedBusiness.city}"`);
      }
      
      // Check website consistency
      if (basicBusiness.website !== enhancedBusiness.website) {
        if (basicBusiness.website && enhancedBusiness.website) {
          issues.push(`Website: "${basicBusiness.website}" vs "${enhancedBusiness.website}"`);
        }
      }
      
      if (issues.length > 0) {
        inconsistencies.push({
          name: basicBusiness.name,
          issues
        });
      }
    }
  });
  
  if (inconsistencies.length > 0) {
    log(`\n⚠ Found ${inconsistencies.length} businesses with data inconsistencies:`, 'red');
    inconsistencies.forEach(item => {
      log(`\n  • ${item.name}:`, 'red');
      item.issues.forEach(issue => {
        log(`    - ${issue}`, 'yellow');
      });
    });
  } else {
    log('\n✓ No data inconsistencies found between matching businesses', 'green');
  }
  
  return inconsistencies;
}

// Check for potential duplicate businesses (similar names)
function checkForDuplicates(businesses) {
  logSection('DUPLICATE BUSINESS CHECK');
  
  const potentialDuplicates = [];
  
  for (let i = 0; i < businesses.length; i++) {
    for (let j = i + 1; j < businesses.length; j++) {
      const business1 = businesses[i];
      const business2 = businesses[j];
      
      // Check for similar names (case-insensitive)
      const name1 = business1.name.toLowerCase().trim();
      const name2 = business2.name.toLowerCase().trim();
      
      // Check for exact matches (shouldn't happen but good to verify)
      if (name1 === name2) {
        potentialDuplicates.push({
          type: 'exact',
          business1,
          business2,
          similarity: 'Exact match'
        });
      }
      // Check for very similar names (potential typos or variations)
      else if (levenshteinDistance(name1, name2) <= 2 && Math.abs(name1.length - name2.length) <= 2) {
        potentialDuplicates.push({
          type: 'similar',
          business1,
          business2,
          similarity: `Similar names (edit distance: ${levenshteinDistance(name1, name2)})`
        });
      }
      // Check for same business in same city with different names
      else if (business1.city === business2.city && business1.category === business2.category) {
        if (name1.includes(name2) || name2.includes(name1)) {
          potentialDuplicates.push({
            type: 'subset',
            business1,
            business2,
            similarity: 'One name contains the other'
          });
        }
      }
    }
  }
  
  if (potentialDuplicates.length > 0) {
    log(`\n⚠ Found ${potentialDuplicates.length} potential duplicate businesses:`, 'yellow');
    potentialDuplicates.forEach((dup, index) => {
      log(`\n  ${index + 1}. ${dup.similarity}:`, 'yellow');
      log(`     • "${dup.business1.name}" (${dup.business1.category}, ${dup.business1.city})`, 'white');
      log(`     • "${dup.business2.name}" (${dup.business2.category}, ${dup.business2.city})`, 'white');
    });
  } else {
    log('\n✓ No potential duplicate businesses found', 'green');
  }
  
  return potentialDuplicates;
}

// Calculate Levenshtein distance between two strings
function levenshteinDistance(str1, str2) {
  const matrix = [];
  
  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }
  
  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }
  
  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        );
      }
    }
  }
  
  return matrix[str2.length][str1.length];
}

// Main audit function
function runBusinessNameAudit() {
  logHeader('BUSINESS NAME AUDIT REPORT');
  log('Analyzing business data for naming consistency and accuracy...', 'cyan');
  
  const { basicData, enhancedData } = loadBusinessData();
  
  if (basicData.length === 0 && enhancedData.length === 0) {
    log('\n✗ No business data found to audit', 'red');
    return;
  }
  
  // Use enhanced data as primary source, fall back to basic if needed
  const primaryData = enhancedData.length > 0 ? enhancedData : basicData;
  
  // Compare datasets if both exist
  if (basicData.length > 0 && enhancedData.length > 0) {
    const { onlyInBasic, onlyInEnhanced } = compareBusinessNames(basicData, enhancedData);
    checkDataInconsistencies(basicData, enhancedData);
  }
  
  // Check for duplicates in primary dataset
  checkForDuplicates(primaryData);
  
  logHeader('AUDIT SUMMARY');
  log(`Total businesses in primary dataset: ${primaryData.length}`, 'cyan');
  log(`Data source: ${enhancedData.length > 0 ? 'Enhanced data file' : 'Basic data file'}`, 'cyan');
  
  // Generate recommendations
  logSection('RECOMMENDATIONS');
  log('1. Review any inconsistencies found above', 'white');
  log('2. Verify potential duplicates manually', 'white');
  log('3. Ensure all business names are current and accurate', 'white');
  log('4. Test search functionality with identified business names', 'white');
  log('5. Consider implementing validation rules for future submissions', 'white');
  
  console.log('\n');
}

// Run the audit
runBusinessNameAudit();

export {
  runBusinessNameAudit,
  loadBusinessData,
  compareBusinessNames,
  checkDataInconsistencies,
  checkForDuplicates
};
