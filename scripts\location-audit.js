#!/usr/bin/env node

/**
 * DFW Business Directory Location Audit Script
 *
 * This script verifies that all businesses in the directory are actually
 * located within the official Dallas-Fort Worth metropolitan area.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Official DFW Metropolitan Area Definition
// Based on U.S. Office of Management and Budget designation
const DFW_METRO_AREA = {
  // Dallas–Plano–Irving metropolitan division
  dallasDivision: {
    counties: ['Collin', 'Dallas', 'Denton', 'Ellis', 'Hunt', 'Kaufman', 'Rockwall'],
    majorCities: [
      '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 'Carroll<PERSON>', 
      '<PERSON>s<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Allen', 'Denton', 'Lewisville',
      'Flower Mound', 'The Colony', 'Farmers Branch', 'Co<PERSON>l', 'Addison',
      'University Park', 'Highland Park', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
      '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>',
      '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON> Elm', 'Rock<PERSON>', '<PERSON>',
      '<PERSON>', 'McLendon-Chisholm', 'Royse City', 'Terrell', 'Forney',
      'Seagoville', 'Balch Springs', 'Sunnyvale', 'Hutchins', 'Wilmer',
      'Lancaster', 'DeSoto', 'Cedar Hill', 'Duncanville', 'Glenn Heights',
      'Red Oak', 'Ovilla', 'Ferris', 'Palmer', 'Ennis', 'Waxahachie',
      'Midlothian', 'Mansfield', 'Grand Prairie'
    ]
  },
  
  // Fort Worth–Arlington–Grapevine metropolitan division  
  fortWorthDivision: {
    counties: ['Johnson', 'Parker', 'Tarrant', 'Wise'],
    majorCities: [
      'Fort Worth', 'Arlington', 'Grapevine', 'Irving', 'Grand Prairie',
      'Euless', 'Bedford', 'Hurst', 'North Richland Hills', 'Keller',
      'Southlake', 'Colleyville', 'Haltom City', 'Watauga', 'Richland Hills',
      'Forest Hill', 'Kennedale', 'Pantego', 'Dalworthington Gardens',
      'Westworth Village', 'River Oaks', 'Sansom Park', 'White Settlement',
      'Westover Hills', 'Benbrook', 'Crowley', 'Burleson', 'Mansfield',
      'Weatherford', 'Azle', 'Saginaw', 'Blue Mound', 'Haslet', 'Roanoke',
      'Trophy Club', 'Westlake', 'Pecan Acres', 'Lakeside', 'Eagle Mountain',
      'Newark', 'Rhome', 'Boyd', 'Springtown', 'Millsap', 'Aledo',
      'Willow Park', 'Hudson Oaks', 'Annetta', 'Cool', 'Poolville',
      'Cleburne', 'Joshua', 'Godley', 'Keene', 'Alvarado', 'Venus',
      'Maypearl', 'Itasca', 'Grandview', 'Covington', 'Hillsboro',
      'Decatur', 'Bridgeport', 'Chico', 'Paradise', 'Runaway Bay',
      'Aurora', 'Rhome', 'New Fairview', 'Justin', 'Argyle', 'Bartonville',
      'Copper Canyon', 'Corinth', 'Lake Dallas', 'Hickory Creek',
      'Shady Shores', 'Oak Point', 'Aubrey', 'Krugerville', 'Cross Roads',
      'Lakewood Village', 'Pilot Point'
    ]
  }
};

// Get all valid DFW cities
const getAllDFWCities = () => {
  return [
    ...DFW_METRO_AREA.dallasDivision.majorCities,
    ...DFW_METRO_AREA.fortWorthDivision.majorCities
  ].map(city => city.toLowerCase());
};

// Cities that are commonly confused as being in DFW but are outside the metro area
const NEARBY_NON_DFW_CITIES = [
  'tyler', 'longview', 'marshall', 'texarkana', 'paris', 'sherman', 'denison',
  'gainesville', 'bonham', 'sulphur springs', 'greenville', 'commerce',
  'athens', 'corsicana', 'palestine', 'huntsville', 'conroe', 'bryan',
  'college station', 'waco', 'killeen', 'temple', 'austin', 'san antonio',
  'houston', 'beaumont', 'port arthur', 'galveston', 'victoria', 'corpus christi',
  'laredo', 'brownsville', 'mcallen', 'el paso', 'lubbock', 'amarillo',
  'abilene', 'san angelo', 'odessa', 'midland', 'wichita falls'
];

// Load business data
const loadBusinessData = () => {
  const basicData = JSON.parse(fs.readFileSync(
    path.join(__dirname, '../src/data/dfw-businesses.json'), 'utf8'
  ));
  
  const enhancedData = JSON.parse(fs.readFileSync(
    path.join(__dirname, '../src/data/dfw-businesses-enhanced.json'), 'utf8'
  ));
  
  return { basicData, enhancedData };
};

// Analyze business location
const analyzeBusinessLocation = (business) => {
  const city = business.city?.toLowerCase().trim();
  const address = business.address?.toLowerCase() || '';
  
  const validDFWCities = getAllDFWCities();
  const isInDFW = validDFWCities.includes(city);
  const isNearbyNonDFW = NEARBY_NON_DFW_CITIES.includes(city);
  
  // Extract state from address if available
  const stateMatch = address.match(/,\s*(tx|texas|ok|oklahoma)\s*\d{5}/i);
  const state = stateMatch ? stateMatch[1].toLowerCase() : null;
  
  return {
    business,
    city,
    state,
    isInDFW,
    isNearbyNonDFW,
    hasCoordinates: !!(business.coordinates?.latitude && business.coordinates?.longitude),
    address: business.address || 'No address provided'
  };
};

// Generate audit report
const generateAuditReport = () => {
  console.log('🔍 DFW Business Directory Location Audit');
  console.log('=' .repeat(50));
  console.log();
  
  const { basicData, enhancedData } = loadBusinessData();
  
  // Combine all businesses for analysis
  const allBusinesses = [...basicData, ...enhancedData];
  const uniqueBusinesses = allBusinesses.filter((business, index, self) => 
    index === self.findIndex(b => b.name === business.name)
  );
  
  console.log(`📊 Total unique businesses analyzed: ${uniqueBusinesses.length}`);
  console.log();
  
  // Analyze each business
  const analysisResults = uniqueBusinesses.map(analyzeBusinessLocation);
  
  // Categorize results
  const validDFWBusinesses = analysisResults.filter(result => result.isInDFW);
  const invalidBusinesses = analysisResults.filter(result => !result.isInDFW);
  const nearbyNonDFWBusinesses = analysisResults.filter(result => result.isNearbyNonDFW);
  const unknownCities = analysisResults.filter(result => 
    !result.isInDFW && !result.isNearbyNonDFW
  );
  
  // Generate city statistics
  const cityStats = {};
  analysisResults.forEach(result => {
    const city = result.city || 'Unknown';
    cityStats[city] = (cityStats[city] || 0) + 1;
  });
  
  // Display results
  console.log('✅ VALID DFW BUSINESSES');
  console.log('-'.repeat(30));
  console.log(`Count: ${validDFWBusinesses.length} (${(validDFWBusinesses.length/uniqueBusinesses.length*100).toFixed(1)}%)`);
  console.log();
  
  if (invalidBusinesses.length > 0) {
    console.log('❌ BUSINESSES OUTSIDE DFW AREA');
    console.log('-'.repeat(30));
    invalidBusinesses.forEach(result => {
      console.log(`• ${result.business.name}`);
      console.log(`  City: ${result.city || 'Unknown'}`);
      console.log(`  Address: ${result.address}`);
      console.log(`  Coordinates: ${result.hasCoordinates ? 'Yes' : 'No'}`);
      console.log();
    });
  }
  
  if (nearbyNonDFWBusinesses.length > 0) {
    console.log('⚠️  BUSINESSES IN NEARBY NON-DFW CITIES');
    console.log('-'.repeat(30));
    nearbyNonDFWBusinesses.forEach(result => {
      console.log(`• ${result.business.name} (${result.city})`);
    });
    console.log();
  }
  
  if (unknownCities.length > 0) {
    console.log('❓ BUSINESSES IN UNKNOWN/UNRECOGNIZED CITIES');
    console.log('-'.repeat(30));
    unknownCities.forEach(result => {
      console.log(`• ${result.business.name} (${result.city || 'No city specified'})`);
    });
    console.log();
  }
  
  console.log('📍 CITY DISTRIBUTION');
  console.log('-'.repeat(30));
  Object.entries(cityStats)
    .sort(([,a], [,b]) => b - a)
    .forEach(([city, count]) => {
      const status = getAllDFWCities().includes(city.toLowerCase()) ? '✅' : '❌';
      console.log(`${status} ${city}: ${count} business${count !== 1 ? 'es' : ''}`);
    });
  
  console.log();
  console.log('📋 AUDIT SUMMARY');
  console.log('-'.repeat(30));
  console.log(`Total businesses: ${uniqueBusinesses.length}`);
  console.log(`Valid DFW businesses: ${validDFWBusinesses.length}`);
  console.log(`Invalid/Outside DFW: ${invalidBusinesses.length}`);
  console.log(`Accuracy rate: ${(validDFWBusinesses.length/uniqueBusinesses.length*100).toFixed(1)}%`);
  
  if (invalidBusinesses.length > 0) {
    console.log();
    console.log('🔧 RECOMMENDATIONS');
    console.log('-'.repeat(30));
    console.log('1. Review businesses marked as outside DFW area');
    console.log('2. Verify addresses and coordinates for flagged businesses');
    console.log('3. Consider removing or relocating non-DFW businesses');
    console.log('4. Update city names to match official DFW area cities');
  }
  
  return {
    total: uniqueBusinesses.length,
    valid: validDFWBusinesses.length,
    invalid: invalidBusinesses.length,
    accuracy: (validDFWBusinesses.length/uniqueBusinesses.length*100).toFixed(1)
  };
};

// Run the audit
if (import.meta.url.startsWith('file://') && process.argv[1] && import.meta.url.includes(process.argv[1].replace(/\\/g, '/'))) {
  try {
    console.log('🚀 Starting DFW Business Directory Location Audit...\n');
    generateAuditReport();
  } catch (error) {
    console.error('❌ Audit failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

export { generateAuditReport, analyzeBusinessLocation, getAllDFWCities };
