export function BusinessCardSkeleton() {
  return (
    <div className="rounded-xl border p-4 shadow-sm bg-white animate-pulse">
      <div className="flex justify-between items-start gap-2 mb-3">
        <div className="flex-1">
          <div className="h-5 bg-gray-200 rounded-md w-3/4 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded-md w-1/2 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded-md w-1/3"></div>
        </div>
        <div className="h-8 w-8 bg-gray-200 rounded"></div>
      </div>
      
      <div className="space-y-2 mb-3">
        <div className="h-3 bg-gray-200 rounded w-full"></div>
        <div className="h-3 bg-gray-200 rounded w-4/5"></div>
      </div>
      
      <div className="flex gap-2">
        <div className="h-4 bg-gray-200 rounded w-16"></div>
        <div className="h-4 bg-gray-200 rounded w-12"></div>
        <div className="h-4 bg-gray-200 rounded w-20 ml-auto"></div>
      </div>
    </div>
  );
}

export function LoadingGrid({ count = 8 }: { count?: number }) {
  return (
    <div className="grid [grid-template-columns:repeat(auto-fill,minmax(280px,1fr))] gap-4">
      {Array.from({ length: count }).map((_, idx) => (
        <BusinessCardSkeleton key={idx} />
      ))}
    </div>
  );
}
