import { Star, ExternalLink, MessageCircle, ThumbsUp } from 'lucide-react';

interface ReviewsProps {
  businessName: string;
  website?: string;
  phone?: string;
  address?: string;
  averageRating?: number;
  totalReviews?: number;
}

export default function Reviews({ 
  businessName, 
  website, 
  phone, 
  address, 
  averageRating = 0, 
  totalReviews = 0 
}: ReviewsProps) {
  const renderStars = (rating: number, size: 'sm' | 'md' = 'sm') => {
    const starSize = size === 'sm' ? 16 : 20;
    return (
      <div className="flex items-center gap-0.5">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            size={starSize}
            className={`${
              star <= rating
                ? 'text-yellow-400 fill-current'
                : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  const generateGoogleReviewsUrl = () => {
    // Create a Google search URL that will show the business and its reviews
    const searchQuery = `${businessName} reviews`;
    if (address) {
      return `https://www.google.com/search?q=${encodeURIComponent(`${businessName} ${address} reviews`)}`;
    }
    return `https://www.google.com/search?q=${encodeURIComponent(searchQuery)}`;
  };

  const generateYelpUrl = () => {
    return `https://www.yelp.com/search?find_desc=${encodeURIComponent(businessName)}`;
  };

  const reviewPlatforms = [
    {
      name: 'Google Reviews',
      icon: <ExternalLink size={20} />,
      url: generateGoogleReviewsUrl(),
      description: 'View and write reviews on Google',
      color: 'bg-blue-500 hover:bg-blue-600'
    },
    {
      name: 'Yelp',
      icon: <MessageCircle size={20} />,
      url: generateYelpUrl(),
      description: 'Check out reviews and photos on Yelp',
      color: 'bg-red-500 hover:bg-red-600'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Current Rating Summary */}
      {averageRating > 0 && (
        <div className="bg-gray-50 rounded-lg p-6 text-center">
          <div className="flex flex-col items-center gap-3">
            <div className="text-4xl font-bold text-gray-900">
              {averageRating.toFixed(1)}
            </div>
            {renderStars(averageRating, 'md')}
            <p className="text-gray-600">
              Based on {totalReviews} review{totalReviews !== 1 ? 's' : ''} across platforms
            </p>
          </div>
        </div>
      )}

      {/* External Review Platforms */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <ThumbsUp size={20} />
          Find Reviews & Leave Feedback
        </h3>
        
        <div className="space-y-3">
          {reviewPlatforms.map((platform) => (
            <a
              key={platform.name}
              href={platform.url}
              target="_blank"
              rel="noopener noreferrer"
              className={`block p-4 rounded-lg border-2 border-transparent hover:border-gray-200 transition-all duration-200 group ${platform.color} text-white hover:shadow-md`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {platform.icon}
                  <div>
                    <h4 className="font-semibold">{platform.name}</h4>
                    <p className="text-sm opacity-90">{platform.description}</p>
                  </div>
                </div>
                <ExternalLink size={16} className="opacity-75 group-hover:opacity-100" />
              </div>
            </a>
          ))}
        </div>
      </div>

      {/* Helpful Information */}
      <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
        <h4 className="font-semibold text-blue-900 mb-2">Help Other Customers</h4>
        <p className="text-blue-800 text-sm">
          Share your experience with {businessName} by leaving a review on Google or Yelp. 
          Your feedback helps other customers make informed decisions and supports local businesses.
        </p>
      </div>
    </div>
  );
}
