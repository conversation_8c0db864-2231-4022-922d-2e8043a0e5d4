import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 DFW Business Directory Location Audit');
console.log('=' .repeat(50));

try {
  // Load business data
  const basicDataPath = path.join(__dirname, '../src/data/dfw-businesses.json');
  const enhancedDataPath = path.join(__dirname, '../src/data/dfw-businesses-enhanced.json');
  
  console.log('Loading data from:', basicDataPath);
  console.log('Loading data from:', enhancedDataPath);
  
  const basicData = JSON.parse(fs.readFileSync(basicDataPath, 'utf8'));
  const enhancedData = JSON.parse(fs.readFileSync(enhancedDataPath, 'utf8'));
  
  console.log(`Basic data businesses: ${basicData.length}`);
  console.log(`Enhanced data businesses: ${enhancedData.length}`);
  
  // Official DFW cities (simplified list)
  const dfwCities = [
    'dallas', 'fort worth', 'arlington', 'plano', 'irving', 'garland', 
    'grand prairie', 'mesquite', 'carrollton', 'richardson', 'mckinney',
    'frisco', 'denton', 'lewisville', 'allen', 'flower mound', 'euless',
    'bedford', 'grapevine', 'hurst', 'north richland hills', 'keller',
    'southlake', 'colleyville', 'mansfield', 'desoto', 'cedar hill',
    'duncanville', 'lancaster', 'farmers branch', 'coppell', 'addison',
    'university park', 'highland park', 'rowlett', 'wylie', 'rockwall',
    'sachse', 'murphy', 'parker', 'fairview', 'princeton', 'anna',
    'melissa', 'celina', 'prosper', 'little elm', 'the colony',
    'weatherford', 'cleburne', 'burleson', 'crowley', 'forest hill',
    'haltom city', 'richland hills', 'watauga', 'benbrook', 'white settlement',
    'azle', 'saginaw', 'kennedale', 'everman', 'sansom park'
  ];
  
  // Combine and deduplicate businesses
  const allBusinesses = [...basicData, ...enhancedData];
  const uniqueBusinesses = allBusinesses.filter((business, index, self) => 
    index === self.findIndex(b => b.name === business.name)
  );
  
  console.log(`\nTotal unique businesses: ${uniqueBusinesses.length}`);
  
  // Analyze each business
  const validBusinesses = [];
  const invalidBusinesses = [];
  const cityStats = {};
  
  uniqueBusinesses.forEach(business => {
    const city = business.city?.toLowerCase().trim() || 'unknown';
    cityStats[city] = (cityStats[city] || 0) + 1;
    
    if (dfwCities.includes(city)) {
      validBusinesses.push(business);
    } else {
      invalidBusinesses.push({
        name: business.name,
        city: business.city || 'Unknown',
        address: business.address || 'No address'
      });
    }
  });
  
  console.log('\n✅ AUDIT RESULTS');
  console.log('-'.repeat(30));
  console.log(`Valid DFW businesses: ${validBusinesses.length}`);
  console.log(`Invalid/Outside DFW: ${invalidBusinesses.length}`);
  console.log(`Accuracy rate: ${(validBusinesses.length/uniqueBusinesses.length*100).toFixed(1)}%`);
  
  if (invalidBusinesses.length > 0) {
    console.log('\n❌ BUSINESSES OUTSIDE DFW AREA:');
    invalidBusinesses.forEach(business => {
      console.log(`• ${business.name} (${business.city})`);
    });
  }
  
  console.log('\n📍 CITY DISTRIBUTION:');
  Object.entries(cityStats)
    .sort(([,a], [,b]) => b - a)
    .forEach(([city, count]) => {
      const status = dfwCities.includes(city.toLowerCase()) ? '✅' : '❌';
      console.log(`${status} ${city}: ${count}`);
    });
    
} catch (error) {
  console.error('❌ Error:', error.message);
  console.error(error.stack);
}
