import { useState, useCallback, RefObject } from 'react';

interface PullToRefreshOptions {
  onRefresh: () => Promise<void>;
  threshold?: number; // Distance in pixels to trigger refresh
}

export function usePullToRefresh({ onRefresh, threshold = 80 }: PullToRefreshOptions) {
  const [isPulling, setIsPulling] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  
  let startY = 0;
  let currentY = 0;
  let isScrolling = false;
  let scrollDirection = 0;

  const handleTouchStart = useCallback((e: TouchEvent) => {
    // Only trigger if user is at the very top of the page
    if (window.scrollY > 5) return;
    
    startY = e.touches[0].clientY;
    isScrolling = false;
    scrollDirection = 0;
  }, []);

  const handleTouchMove = useCallback((e: TouchEvent) => {
    // Don't trigger if not at top or already scrolling down
    if (window.scrollY > 5) {
      setIsPulling(false);
      setPullDistance(0);
      return;
    }
    
    currentY = e.touches[0].clientY;
    const distance = currentY - startY;
    scrollDirection = distance;
    
    // Only start pulling if moving significantly downward and not scrolling
    if (distance > 10 && !isScrolling) {
      setIsPulling(true);
      // Only prevent default and set pull distance if we're actually pulling down
      if (isPulling) {
        e.preventDefault();
        setPullDistance(Math.max(0, distance));
      }
    } else if (distance < -10) {
      // User is scrolling up, don't interfere
      isScrolling = true;
      setIsPulling(false);
      setPullDistance(0);
    }
  }, [isPulling]);

  const handleTouchEnd = useCallback(async () => {
    if (!isPulling && pullDistance === 0) return;
    
    setIsPulling(false);
    
    if (pullDistance >= threshold && !isScrolling) {
      setIsRefreshing(true);
      try {
        await onRefresh();
      } finally {
        setIsRefreshing(false);
      }
    }
    
    setPullDistance(0);
    isScrolling = false;
    scrollDirection = 0;
  }, [isPulling, pullDistance, threshold, onRefresh]);

  const bindToElement = useCallback((element: HTMLElement | null) => {
    if (!element) return;
    
    // Use passive: false only for touchmove to allow preventDefault when needed
    element.addEventListener('touchstart', handleTouchStart, { passive: true });
    element.addEventListener('touchmove', handleTouchMove, { passive: false });
    element.addEventListener('touchend', handleTouchEnd, { passive: true });
    
    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
      element.removeEventListener('touchend', handleTouchEnd);
    };
  }, [handleTouchStart, handleTouchMove, handleTouchEnd]);

  return {
    isPulling,
    isRefreshing,
    pullDistance,
    bindToElement,
  };
}
