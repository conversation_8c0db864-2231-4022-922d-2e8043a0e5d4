// @deprecated - Reviews are now handled through external platforms
export type Review = {
  id: string;
  author: string;
  rating: number;
  comment: string;
  date: string;
  verified?: boolean;
};

export type Business = {
  id?: string;
  name: string;
  category: string;
  city: string;
  website?: string;
  phone?: string;
  address?: string;
  description?: string;
  social?: Record<string, string>;
  status?: 'pending' | 'approved' | 'rejected';
  hours?: {
    monday?: string;
    tuesday?: string;
    wednesday?: string;
    thursday?: string;
    friday?: string;
    saturday?: string;
    sunday?: string;
  };
  rating?: number;
  reviewCount?: number;
  email?: string;
  price?: '$' | '$$' | '$$$' | '$$$$';
  features?: string[];
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  distance?: number; // Distance from user's location in miles
};

