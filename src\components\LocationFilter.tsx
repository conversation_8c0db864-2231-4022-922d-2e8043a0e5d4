import React from 'react';
import { MapPin, Navigation, X } from 'lucide-react';
import { useGeolocation } from '../hooks/useGeolocation';

type LocationFilterProps = {
  onLocationSet: (latitude: number, longitude: number) => void;
  onLocationClear: () => void;
  hasLocation: boolean;
  isLoading?: boolean;
};

export function LocationFilter({ 
  onLocationSet, 
  onLocationClear, 
  hasLocation,
  isLoading = false
}: LocationFilterProps) {
  const { 
    latitude, 
    longitude, 
    error, 
    loading, 
    getCurrentLocation, 
    clearLocation 
  } = useGeolocation();

  const handleGetLocation = () => {
    getCurrentLocation();
  };

  const handleClearLocation = () => {
    clearLocation();
    onLocationClear();
  };

  // Effect to pass location to parent when available
  React.useEffect(() => {
    if (latitude && longitude) {
      onLocationSet(latitude, longitude);
    }
  }, [latitude, longitude, onLocationSet]);

  return (
    <div className="flex items-center gap-2">
      {!hasLocation ? (
        <button
          onClick={handleGetLocation}
          disabled={loading || isLoading}
          className="flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors text-sm font-medium"
          title="Find businesses near your location"
        >
          {loading ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              Getting Location...
            </>
          ) : (
            <>
              <Navigation size={16} />
              Near Me
            </>
          )}
        </button>
      ) : (
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1 px-3 py-2 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-lg text-sm font-medium">
            <MapPin size={16} />
            Near Me
          </div>
          <button
            onClick={handleClearLocation}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            title="Clear location filter"
          >
            <X size={16} />
          </button>
        </div>
      )}
      
      {error && (
        <div className="text-red-600 dark:text-red-400 text-sm max-w-xs">
          {error}
        </div>
      )}
    </div>
  );
}
