const RECENTLY_VIEWED_KEY = 'dfwbb:recently-viewed';

// Recently Viewed functionality
export function getRecentlyViewed(): string[] {
  try {
    return JSON.parse(localStorage.getItem(RECENTLY_VIEWED_KEY) || '[]');
  } catch {
    return [];
  }
}

export function addToRecentlyViewed(businessName: string): void {
  const recentlyViewed = getRecentlyViewed();
  
  // Remove if already exists to avoid duplicates
  const index = recentlyViewed.indexOf(businessName);
  if (index > -1) {
    recentlyViewed.splice(index, 1);
  }
  
  // Add to beginning of array
  recentlyViewed.unshift(businessName);
  
  // Keep only last 10 items
  const trimmed = recentlyViewed.slice(0, 10);
  
  localStorage.setItem(RECENTLY_VIEWED_KEY, JSON.stringify(trimmed));
}

export function clearRecentlyViewed(): void {
  localStorage.removeItem(RECENTLY_VIEWED_KEY);
}

