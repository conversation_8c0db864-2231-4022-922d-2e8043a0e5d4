import { Business } from '../types';
import { isBusinessOpen } from '../lib/businessUtils';
import { useFavorites } from '../hooks/useFavorites';
import FavoriteButton from './FavoriteButton';
import Reviews from './Reviews';
import { useState } from 'react';
import { 
  X, 
  MapPin, 
  Phone, 
  Globe, 
  Clock, 
  Star, 
  Share2, 
  ExternalLink,
  Instagram,
  Facebook
} from 'lucide-react';

interface BusinessModalProps {
  business: Business | null;
  isOpen: boolean;
  onClose: () => void;
}

export function BusinessModal({ business, isOpen, onClose }: BusinessModalProps) {
  const [activeTab, setActiveTab] = useState<'info' | 'reviews'>('info');
  const { isFavorite, toggleFavorite } = useFavorites();

  if (!isOpen || !business) return null;

  const onFav = () => {
    toggleFavorite(business.name);
  };

  const share = async () => {
    const text = `Check out ${business.name} in ${business.city}!`;
    const url = business.website || window.location.href;
    try {
      if (navigator.share) { 
        await navigator.share({ title: business.name, text, url }); 
      } else { 
        await navigator.clipboard.writeText(url); 
        alert('Link copied to clipboard!'); 
      }
    } catch {}
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star 
        key={i} 
        size={16}
        className={`${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  const formatPhoneNumber = (phone: string) => {
    const cleaned = phone.replace(/\D/g, '');
    const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
    if (match) {
      return `(${match[1]}) ${match[2]}-${match[3]}`;
    }
    return phone;
  };

  const getBusinessHours = () => {
    if (!business.hours) return null;
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    const dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    
    return days.map((day, index) => ({
      day: dayNames[index],
      hours: business.hours?.[day as keyof typeof business.hours] || 'Closed'
    }));
  };

  const businessStatus = business ? isBusinessOpen(business) : null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50" onClick={onClose}>
      <div 
        className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="p-6 border-b bg-white">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <h2 className="text-2xl font-bold text-gray-900">{business.name}</h2>
              <p className="text-sm text-gray-600 mt-1">{business.category} • {business.city}</p>
              
              {/* Business Status */}
              {businessStatus && (
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium mt-2 ${
                  businessStatus.isOpen 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  <div className={`w-2 h-2 rounded-full mr-2 ${
                    businessStatus.isOpen ? 'bg-green-400' : 'bg-red-400'
                  }`}></div>
                  {businessStatus.status}
                  {businessStatus.nextChange && (
                    <span className="ml-1 text-xs opacity-75">• {businessStatus.nextChange}</span>
                  )}
                </div>
              )}
              
              {/* Rating */}
              {business.rating && (
                <div className="flex items-center mt-2">
                  <div className="flex mr-2">
                    {renderStars(Math.floor(business.rating))}
                  </div>
                  <span className="text-sm text-gray-600">
                    {business.rating.toFixed(1)} ({business.reviewCount || 0} reviews)
                  </span>
                </div>
              )}

              {/* Price Level */}
              {business.price && (
                <div className="mt-2">
                  <span className="text-green-600 font-medium">{business.price}</span>
                </div>
              )}
            </div>
            
            <div className="flex gap-2 ml-4">
              <FavoriteButton 
                isFavorited={isFavorite(business.name)}
                onToggle={onFav}
              />
              <button 
                onClick={share}
                className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
                aria-label="Share business"
              >
                <Share2 size={16} />
              </button>
              <button 
                onClick={onClose}
                className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
                aria-label="Close modal"
              >
                <X size={16} />
              </button>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex border-b bg-gray-50">
          <button
            className={`flex-1 px-4 py-3 text-sm font-medium ${
              activeTab === 'info'
                ? 'border-b-2 border-blue-500 bg-white text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('info')}
          >
            Business Info
          </button>
          <button
            className={`flex-1 px-4 py-3 text-sm font-medium ${
              activeTab === 'reviews'
                ? 'border-b-2 border-blue-500 bg-white text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('reviews')}
          >
            Reviews ({business.reviewCount || 0})
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {activeTab === 'info' && (
            <div className="space-y-6">
              {/* Description */}
              {business.description && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">About</h3>
                  <p className="text-gray-700">{business.description}</p>
                </div>
              )}

              {/* Features */}
              {business.features && business.features.length > 0 && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Features</h3>
                  <div className="flex flex-wrap gap-2">
                    {business.features.map((feature, index) => (
                      <span 
                        key={index}
                        className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                      >
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Contact Information */}
              <div>
                <h3 className="font-semibold text-gray-900 mb-3">Contact Information</h3>
                <div className="space-y-3">
                  {business.address && (
                    <div className="flex items-start gap-3">
                      <MapPin size={20} className="text-gray-500 mt-0.5" />
                      <div className="flex-1">
                        <span className="text-gray-700">{business.address}</span>
                        <br />
                        <button
                          onClick={() => {
                            const query = encodeURIComponent(`${business.name} ${business.address}`);
                            window.open(`https://maps.google.com?q=${query}`, '_blank');
                          }}
                          className="text-blue-600 hover:underline text-sm mt-1 inline-flex items-center gap-1"
                        >
                          View on Google Maps <ExternalLink size={14} />
                        </button>
                      </div>
                    </div>
                  )}
                  
                  {business.phone && (
                    <div className="flex items-center gap-3">
                      <Phone size={20} className="text-gray-500" />
                      <a 
                        href={`tel:${business.phone}`}
                        className="text-blue-600 hover:underline"
                      >
                        {formatPhoneNumber(business.phone)}
                      </a>
                    </div>
                  )}
                  
                  {business.website && (
                    <div className="flex items-center gap-3">
                      <Globe size={20} className="text-gray-500" />
                      <a 
                        href={business.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline inline-flex items-center gap-1"
                      >
                        Visit Website <ExternalLink size={14} />
                      </a>
                    </div>
                  )}
                </div>
              </div>

              {/* Business Hours */}
              {business.hours && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                    <Clock size={20} />
                    Hours
                  </h3>
                  <div className="space-y-1 bg-gray-50 rounded-lg p-4">
                    {getBusinessHours()?.map(({ day, hours }) => (
                      <div key={day} className="flex justify-between">
                        <span className="text-gray-600 font-medium">{day}:</span>
                        <span className="text-gray-700">{hours}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Social Media */}
              {business.social && Object.keys(business.social).length > 0 && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3">Social Media</h3>
                  <div className="flex gap-3">
                    {Object.entries(business.social).map(([platform, url]) => {
                      const Icon = platform === 'instagram' ? Instagram : Facebook;
                      return (
                        <a
                          key={platform}
                          href={url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center gap-2 px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm capitalize transition-colors"
                        >
                          <Icon size={16} />
                          {platform}
                        </a>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'reviews' && (
            <Reviews 
              businessName={business.name}
              website={business.website}
              phone={business.phone}
              address={business.address}
              averageRating={business.rating}
              totalReviews={business.reviewCount}
            />
          )}
        </div>

        {/* Action Buttons */}
        <div className="p-4 border-t bg-gray-50 flex gap-3">
          {business.phone && (
            <button
              onClick={() => window.open(`tel:${business.phone}`)}
              className="flex-1 bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors font-medium"
            >
              Call Now
            </button>
          )}
          
          {business.website && (
            <button
              onClick={() => window.open(business.website, '_blank')}
              className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              Visit Website
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
