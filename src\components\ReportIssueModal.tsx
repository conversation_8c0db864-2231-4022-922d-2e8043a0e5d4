import { useState } from 'react';
import { AlertTriangle, X, Send } from 'lucide-react';
import { reportBusinessIssue, type BusinessChangeType } from '../lib/businessChangeTracker';

type ReportIssueModalProps = {
  isOpen: boolean;
  onClose: () => void;
  businessId: string;
  businessName: string;
};

export function ReportIssueModal({ isOpen, onClose, businessId, businessName }: ReportIssueModalProps) {
  const [issueType, setIssueType] = useState<BusinessChangeType>('phone_change');
  const [description, setDescription] = useState('');
  const [reporterName, setReporterName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const issueTypes: { value: BusinessChangeType; label: string }[] = [
    { value: 'phone_change', label: 'Phone Number Issue' },
    { value: 'address_change', label: 'Address Issue' },
    { value: 'location_change', label: 'Location Issue' },
    { value: 'website_change', label: 'Website Issue' },
    { value: 'hours_change', label: 'Hours Issue' },
    { value: 'status_change', label: 'Business Status Issue' },
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!description.trim()) return;

    setIsSubmitting(true);
    
    try {
      reportBusinessIssue(
        businessId,
        businessName,
        issueType,
        description.trim(),
        reporterName.trim() || undefined
      );
      
      // Reset form
      setDescription('');
      setReporterName('');
      setIssueType('phone_change');
      
      // Show success message (you might want to use your notification system)
      alert('Thank you for reporting this issue! We will review it shortly.');
      
      onClose();
    } catch (error) {
      alert('There was an error submitting your report. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-4 border-b dark:border-gray-700">
          <div className="flex items-center gap-2">
            <AlertTriangle className="text-yellow-600 dark:text-yellow-400" size={20} />
            <h2 className="text-lg font-semibold">Report an Issue</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        <div className="p-4">
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            Help us keep business information accurate by reporting any issues you find with <strong>{businessName}</strong>.
          </p>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                Issue Type
              </label>
              <select
                value={issueType}
                onChange={(e) => setIssueType(e.target.value as BusinessChangeType)}
                className="w-full px-3 py-2 border dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {issueTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Description *
              </label>
              <textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Please describe the issue in detail..."
                rows={4}
                required
                className="w-full px-3 py-2 border dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Your Name (Optional)
              </label>
              <input
                type="text"
                value={reporterName}
                onChange={(e) => setReporterName(e.target.value)}
                placeholder="Enter your name (optional)"
                className="w-full px-3 py-2 border dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div className="flex gap-2 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 border dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={!description.trim() || isSubmitting}
                className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                {isSubmitting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    Submitting...
                  </>
                ) : (
                  <>
                    <Send size={16} />
                    Submit Report
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
