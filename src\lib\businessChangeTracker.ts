import type { Business } from '../types';

// Types for business change tracking
export type BusinessChangeType = 
  | 'location_change' 
  | 'phone_change' 
  | 'hours_change' 
  | 'status_change'
  | 'address_change'
  | 'website_change';

export type BusinessChange = {
  id: string;
  businessId: string;
  businessName: string;
  changeType: BusinessChangeType;
  oldValue: string;
  newValue: string;
  detectedAt: string;
  reportedBy?: string; // User who reported the change
  verified: boolean;
  source: 'user_report' | 'automated_check' | 'business_owner';
};

// Store for tracking business changes
const CHANGES_KEY = 'dfwbb:business-changes';
const LAST_CHECK_KEY = 'dfwbb:last-business-check';
const BUSINESS_SNAPSHOTS_KEY = 'dfwbb:business-snapshots';

export function getBusinessChanges(): BusinessChange[] {
  try {
    return JSON.parse(localStorage.getItem(CHANGES_KEY) || '[]');
  } catch {
    return [];
  }
}

export function addBusinessChange(change: Omit<BusinessChange, 'id' | 'detectedAt'>): void {
  const changes = getBusinessChanges();
  const newChange: BusinessChange = {
    ...change,
    id: crypto.randomUUID(),
    detectedAt: new Date().toISOString(),
  };
  
  changes.unshift(newChange);
  
  // Keep only last 100 changes
  const trimmed = changes.slice(0, 100);
  localStorage.setItem(CHANGES_KEY, JSON.stringify(trimmed));
}

export function markChangeAsVerified(changeId: string): void {
  const changes = getBusinessChanges();
  const change = changes.find(c => c.id === changeId);
  if (change) {
    change.verified = true;
    localStorage.setItem(CHANGES_KEY, JSON.stringify(changes));
  }
}

// Store business snapshots for comparison
export function storeBusinessSnapshot(businesses: Business[]): void {
  const snapshot = {
    timestamp: new Date().toISOString(),
    businesses: businesses.map(b => ({
      id: b.id,
      name: b.name,
      phone: b.phone,
      address: b.address,
      website: b.website,
      coordinates: b.coordinates,
      status: b.status,
      hours: b.hours
    }))
  };
  
  localStorage.setItem(BUSINESS_SNAPSHOTS_KEY, JSON.stringify(snapshot));
  localStorage.setItem(LAST_CHECK_KEY, new Date().toISOString());
}

export function getLastBusinessSnapshot() {
  try {
    return JSON.parse(localStorage.getItem(BUSINESS_SNAPSHOTS_KEY) || 'null');
  } catch {
    return null;
  }
}

export function getLastCheckDate(): Date | null {
  try {
    const lastCheck = localStorage.getItem(LAST_CHECK_KEY);
    return lastCheck ? new Date(lastCheck) : null;
  } catch {
    return null;
  }
}

// Compare current businesses with stored snapshot to detect changes
export function detectBusinessChanges(currentBusinesses: Business[]): BusinessChange[] {
  const lastSnapshot = getLastBusinessSnapshot();
  if (!lastSnapshot) {
    // First time - store snapshot and return empty changes
    storeBusinessSnapshot(currentBusinesses);
    return [];
  }

  const detectedChanges: Omit<BusinessChange, 'id' | 'detectedAt'>[] = [];
  
  currentBusinesses.forEach(currentBusiness => {
    if (!currentBusiness.id) return;
    
    const previousBusiness = lastSnapshot.businesses.find((b: any) => b.id === currentBusiness.id);
    if (!previousBusiness) return; // New business, not a change
    
    // Check for phone changes
    if (previousBusiness.phone !== currentBusiness.phone) {
      detectedChanges.push({
        businessId: currentBusiness.id,
        businessName: currentBusiness.name,
        changeType: 'phone_change',
        oldValue: previousBusiness.phone || 'Not provided',
        newValue: currentBusiness.phone || 'Not provided',
        verified: false,
        source: 'automated_check'
      });
    }
    
    // Check for address changes
    if (previousBusiness.address !== currentBusiness.address) {
      detectedChanges.push({
        businessId: currentBusiness.id,
        businessName: currentBusiness.name,
        changeType: 'address_change',
        oldValue: previousBusiness.address || 'Not provided',
        newValue: currentBusiness.address || 'Not provided',
        verified: false,
        source: 'automated_check'
      });
    }
    
    // Check for website changes
    if (previousBusiness.website !== currentBusiness.website) {
      detectedChanges.push({
        businessId: currentBusiness.id,
        businessName: currentBusiness.name,
        changeType: 'website_change',
        oldValue: previousBusiness.website || 'Not provided',
        newValue: currentBusiness.website || 'Not provided',
        verified: false,
        source: 'automated_check'
      });
    }
    
    // Check for location changes (coordinates)
    const oldCoords = previousBusiness.coordinates;
    const newCoords = currentBusiness.coordinates;
    if (oldCoords && newCoords && 
        (oldCoords.latitude !== newCoords.latitude || oldCoords.longitude !== newCoords.longitude)) {
      detectedChanges.push({
        businessId: currentBusiness.id,
        businessName: currentBusiness.name,
        changeType: 'location_change',
        oldValue: `${oldCoords.latitude}, ${oldCoords.longitude}`,
        newValue: `${newCoords.latitude}, ${newCoords.longitude}`,
        verified: false,
        source: 'automated_check'
      });
    }
    
    // Check for status changes
    if (previousBusiness.status !== currentBusiness.status) {
      detectedChanges.push({
        businessId: currentBusiness.id,
        businessName: currentBusiness.name,
        changeType: 'status_change',
        oldValue: previousBusiness.status || 'unknown',
        newValue: currentBusiness.status || 'unknown',
        verified: false,
        source: 'automated_check'
      });
    }
  });
  
  // Store detected changes
  detectedChanges.forEach(change => addBusinessChange(change));
  
  // Update snapshot
  storeBusinessSnapshot(currentBusinesses);
  
  return detectedChanges.map(change => ({
    ...change,
    id: crypto.randomUUID(),
    detectedAt: new Date().toISOString(),
  }));
}

// Report incorrect information
export function reportBusinessIssue(
  businessId: string,
  businessName: string,
  issueType: BusinessChangeType,
  description: string,
  reportedBy?: string
): void {
  addBusinessChange({
    businessId,
    businessName,
    changeType: issueType,
    oldValue: 'Current information',
    newValue: description,
    verified: false,
    source: 'user_report',
    reportedBy
  });
}

export function clearBusinessChanges(): void {
  localStorage.removeItem(CHANGES_KEY);
  localStorage.removeItem(BUSINESS_SNAPSHOTS_KEY);
  localStorage.removeItem(LAST_CHECK_KEY);
}
