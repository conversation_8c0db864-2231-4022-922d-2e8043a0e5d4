import { Heart } from 'lucide-react';

interface FavoriteButtonProps {
  isFavorited: boolean;
  onToggle: () => void;
  size?: 'sm' | 'md' | 'lg';
}

export default function FavoriteButton({ isFavorited, onToggle, size = 'md' }: FavoriteButtonProps) {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-10 h-10'
  };

  const iconSizes = {
    sm: 16,
    md: 20,
    lg: 24
  };

  return (
    <button
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
        onToggle();
      }}
      className={`
        ${sizeClasses[size]} 
        flex items-center justify-center 
        rounded-full 
        transition-all duration-200 
        ${isFavorited 
          ? 'bg-red-100 text-red-600 hover:bg-red-200' 
          : 'bg-gray-100 text-gray-400 hover:bg-gray-200 hover:text-red-500'
        }
      `}
      aria-label={isFavorited ? 'Remove from favorites' : 'Add to favorites'}
      title={isFavorited ? 'Remove from favorites' : 'Add to favorites'}
    >
      <Heart 
        size={iconSizes[size]} 
        fill={isFavorited ? 'currentColor' : 'none'} 
        className="transition-colors duration-200"
      />
    </button>
  );
}
