@tailwind base;
@tailwind components;
@tailwind utilities;

:root { 
  color-scheme: light dark; 
}

/* Reset & containment */
html {
  box-sizing: border-box;
  -webkit-text-size-adjust: 100%;
}

*, *::before, *::after { box-sizing: inherit; }

body {
  background-color: theme('colors.gray.50');
  margin: 0;
  font-family: system-ui, sans-serif;
  overflow-x: hidden; /* prevent horizontal scroll */
}

body.dark {
  background-color: theme('colors.gray.900');
  color: theme('colors.white');
}

#root { min-height: 100dvh; overflow-x: hidden; }

/* Custom utilities for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Ensure bottom navigation stays fixed and doesn't interfere with scrolling */
.safe-area-pb {
  padding-bottom: env(safe-area-inset-bottom);
}

@media (max-width: 640px) {
  body { overscroll-behavior-y: contain; -webkit-overflow-scrolling: touch; }
  .flex { min-width: 0; }
  .grid { min-width: 0; }
  .grid > * { min-width: 0; }
  /* Stabilize fixed bottom nav */
  nav[data-mobile-bottom] { position: fixed; left:0; right:0; bottom:0; }
}
