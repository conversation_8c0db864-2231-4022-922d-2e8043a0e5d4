import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Off, Setting<PERSON> } from 'lucide-react';
import { 
  isPushNotificationSupported, 
  isPushNotificationGranted,
  subscribeToPushNotifications,
  unsubscribeFromPushNotifications,
  startBusinessMonitoring,
  stopBusinessMonitoring
} from '../lib/pushNotifications';

export function PushNotificationSettings() {
  const [isSupported, setIsSupported] = useState(false);
  const [isGranted, setIsGranted] = useState(false);
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const supported = isPushNotificationSupported();
    const granted = isPushNotificationGranted();
    
    setIsSupported(supported);
    setIsGranted(granted);
    
    // Check if already subscribed
    const subscription = localStorage.getItem('dfwbb:push-subscription');
    setIsSubscribed(!!subscription && granted);
  }, []);

  const handleSubscribe = async () => {
    setIsLoading(true);
    try {
      // First check if notifications are supported
      if (!('Notification' in window)) {
        alert('Push notifications are not supported in this browser.');
        return;
      }

      // Request permission first
      const permission = await Notification.requestPermission();
      if (permission !== 'granted') {
        alert('Notification permission was denied. Please enable notifications in your browser settings and try again.');
        return;
      }

      const subscription = await subscribeToPushNotifications();
      if (subscription) {
        setIsSubscribed(true);
        setIsGranted(true);
        startBusinessMonitoring();
        
        // Show success message
        alert('Push notifications enabled! You\'ll be notified of new businesses.');
      } else {
        alert('Failed to enable push notifications. Please make sure notifications are allowed in your browser settings.');
      }
    } catch (error) {
      console.error('Subscription failed:', error);
      alert('Failed to enable push notifications. Please check your browser settings and try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleUnsubscribe = async () => {
    setIsLoading(true);
    try {
      const success = await unsubscribeFromPushNotifications();
      if (success) {
        setIsSubscribed(false);
        stopBusinessMonitoring();
        alert('Push notifications disabled.');
      } else {
        alert('Failed to disable notifications.');
      }
    } catch (error) {
      console.error('Unsubscription failed:', error);
      alert('Failed to disable notifications. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isSupported) {
    return (
      <div className="flex items-center gap-2 text-gray-500 text-sm">
        <BellOff size={16} />
        <span>Push notifications not supported in this browser</span>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2">
      {!isSubscribed ? (
        <button
          onClick={handleSubscribe}
          disabled={isLoading}
          className="flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white text-sm rounded-lg transition-colors"
          title="Get notified of new businesses"
        >
          {isLoading ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              Enabling...
            </>
          ) : (
            <>
              <Bell size={16} />
              Enable Notifications
            </>
          )}
        </button>
      ) : (
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1 px-3 py-2 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-lg text-sm">
            <Bell size={16} />
            <span>Notifications On</span>
          </div>
          <button
            onClick={handleUnsubscribe}
            disabled={isLoading}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            title="Disable notifications"
          >
            <Settings size={16} />
          </button>
        </div>
      )}
    </div>
  );
}
