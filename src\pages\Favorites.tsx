import { useState } from 'react';
import { useFavorites } from '../hooks/useFavorites';
import { BusinessCard } from '../components/BusinessCard';
import { BusinessModal } from '../components/BusinessModal';
import { Business } from '../types';
import { Heart, Trash2 } from 'lucide-react';
import dfwBusinesses from '../data/dfw-businesses-enhanced.json';

export default function Favorites() {
  const { favorites, removeFromFavorites } = useFavorites();
  const [selectedBusiness, setSelectedBusiness] = useState<Business | null>(null);

  // Get business objects from names stored in favorites
  const favoriteBusinesses = favorites
    .map(fav => {
      const business = dfwBusinesses.find(b => b.name === fav.businessName);
      return business as Business | undefined;
    })
    .filter((business): business is Business => business !== undefined)
    .sort((a, b) => {
      const favA = favorites.find(f => f.businessName === a.name);
      const favB = favorites.find(f => f.businessName === b.name);
      if (!favA || !favB) return 0;
      return new Date(favB.addedAt).getTime() - new Date(favA.addedAt).getTime();
    });

  const clearAllFavorites = () => {
    if (window.confirm('Are you sure you want to remove all favorites?')) {
      favorites.forEach(fav => removeFromFavorites(fav.businessName));
    }
  };

  if (favoriteBusinesses.length === 0) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center py-12">
          <Heart size={64} className="mx-auto text-gray-300 dark:text-gray-600 mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">No Favorites Yet</h1>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            Start exploring businesses and add them to your favorites by clicking the heart icon.
          </p>
          <a 
            href="/" 
            className="inline-block bg-blue-600 dark:bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors"
          >
            Browse Directory
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">My Favorites</h1>
          <p className="text-gray-600 dark:text-gray-300 mt-1">
            {favoriteBusinesses.length} saved business{favoriteBusinesses.length !== 1 ? 'es' : ''}
          </p>
        </div>
        
        {favoriteBusinesses.length > 0 && (
          <button
            onClick={clearAllFavorites}
            className="flex items-center gap-2 px-4 py-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
          >
            <Trash2 size={16} />
            Clear All
          </button>
        )}
      </div>

      <div className="grid gap-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        {favoriteBusinesses.map((business) => {
          const favorite = favorites.find(f => f.businessName === business.name);
          if (!favorite) return null;
          
          return (
            <div key={business.name} className="relative">
              <BusinessCard 
                b={business} 
                onClick={() => setSelectedBusiness(business)}
              />
              <div className="absolute top-2 right-2 text-xs text-gray-500 dark:text-gray-400 bg-white/90 dark:bg-gray-800/90 px-2 py-1 rounded backdrop-blur-sm">
                Added {new Date(favorite.addedAt).toLocaleDateString()}
              </div>
            </div>
          );
        })}
      </div>

      <BusinessModal
        business={selectedBusiness}
        isOpen={!!selectedBusiness}
        onClose={() => setSelectedBusiness(null)}
      />
    </div>
  );
}
