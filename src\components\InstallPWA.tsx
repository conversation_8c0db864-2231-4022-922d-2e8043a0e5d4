import { useState, useEffect } from 'react';
import { X, Download, Share, Plus, ChevronUp } from 'lucide-react';

interface BeforeInstallPromptEvent extends Event {
  prompt(): Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

declare global {
  interface WindowEventMap {
    beforeinstallprompt: BeforeInstallPromptEvent;
  }
}

export default function InstallPWA() {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [isIOS, setIsIOS] = useState(false);
  const [isAndroid, setIsAndroid] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);

  useEffect(() => {
    // Detect device types
    const userAgent = navigator.userAgent;
    const isIOSDevice = /iPad|iPhone|iPod/.test(userAgent) && !(window as any).MSStream;
    const isAndroidDevice = /Android/.test(userAgent);
    
    setIsIOS(isIOSDevice);
    setIsAndroid(isAndroidDevice);

    // Check if already installed (PWA mode)
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
    const isIOSStandalone = (navigator as any).standalone === true;
    
    if (isStandalone || isIOSStandalone) {
      setIsInstalled(true);
    }

    // Listen for beforeinstallprompt event (Android/Windows Chrome, Edge, etc.)
    const handleBeforeInstallPrompt = (e: BeforeInstallPromptEvent) => {
      console.log('beforeinstallprompt event fired');
      e.preventDefault();
      setDeferredPrompt(e);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    // Listen for app installed event
    window.addEventListener('appinstalled', () => {
      console.log('App was installed');
      setIsInstalled(true);
      setDeferredPrompt(null);
    });

    // Debug logging
    console.log('PWA Install Component:', {
      isIOSDevice,
      isAndroidDevice,
      isStandalone,
      isIOSStandalone,
      userAgent: navigator.userAgent
    });

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, []);

  const handleInstallClick = async () => {
    console.log('Install button clicked:', { isIOS, deferredPrompt: !!deferredPrompt });
    
    // iOS devices - always show instructions modal
    if (isIOS) {
      setShowModal(true);
      return;
    }

    // Android/Windows - try native install prompt first
    if (deferredPrompt) {
      try {
        console.log('Triggering install prompt');
        await deferredPrompt.prompt();
        const choiceResult = await deferredPrompt.userChoice;
        
        console.log('User choice:', choiceResult.outcome);
        
        if (choiceResult.outcome === 'accepted') {
          setIsInstalled(true);
        }
        
        setDeferredPrompt(null);
      } catch (error) {
        console.error('Error during installation:', error);
      }
    } else {
      console.log('No deferred prompt available. This could mean:');
      console.log('1. PWA criteria not met');
      console.log('2. App already installed');
      console.log('3. Browser doesn\'t support PWA installation');
      console.log('4. Running in development mode');
    }
    // For Android/Windows: if no deferredPrompt, do nothing (browser will handle it or it's not available)
  };

  // Don't show if already installed
  if (isInstalled) return null;

  const getButtonText = () => {
    if (isIOS) return 'Install App';
    return 'Install App';
  };

  // Show debug info in development
  const isDev = import.meta.env.DEV;

  return (
    <>
      <button
        onClick={handleInstallClick}
        className="flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors"
        title={isDev && !deferredPrompt && !isIOS ? 'No install prompt (check console)' : 'Install App'}
      >
        <Download size={16} />
        <span className="hidden sm:inline">{getButtonText()}</span>
        {isDev && !deferredPrompt && !isIOS && (
          <span className="text-xs opacity-75">🔧</span>
        )}
      </button>

      {showModal && isIOS && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Install App</h3>
              <button
                onClick={() => setShowModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={24} />
              </button>
            </div>

            <div className="space-y-4">
              <p className="text-gray-600">
                To install this app on your iPhone or iPad:
              </p>
              <ol className="space-y-3 text-sm">
                <li className="flex items-start gap-3">
                  <span className="bg-blue-100 text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-semibold">
                    1
                  </span>
                  <div>
                    Tap the <Share size={16} className="inline mx-1" /> 
                    <strong>Share</strong> button at the bottom of Safari
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <span className="bg-blue-100 text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-semibold">
                    2
                  </span>
                  <div>
                    Scroll down and tap 
                    <Plus size={16} className="inline mx-1" />
                    <strong>"Add to Home Screen"</strong>
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <span className="bg-blue-100 text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-semibold">
                    3
                  </span>
                  <div>
                    Tap <strong>"Add"</strong> to confirm
                  </div>
                </li>
              </ol>
              <p className="text-xs text-gray-500 mt-4">
                The app will appear on your home screen and work just like a native app!
              </p>
            </div>

            <button
              onClick={() => setShowModal(false)}
              className="w-full mt-6 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg font-medium transition-colors"
            >
              Got it!
            </button>
          </div>
        </div>
      )}
    </>
  );
}
