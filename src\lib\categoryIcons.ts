import {
  Utensils,          // Restaurant, Food
  ShoppingBag,       // Retail, Shopping
  Scissors,          // Hair, Beauty, Barber
  Wrench,           // Auto, Repair, Services
  GraduationCap,    // Education, Training
  Heart,            // Health, Medical, Fitness
  Palette,          // Arts, Entertainment, Creative
  Users,            // Community, Professional Services
  Home,             // Real Estate, Home Services
  Briefcase,        // Business, Finance, Legal
  Camera,           // Photography, Media
  Truck,            // Transportation, Delivery
  Coffee,           // Café, Coffee Shop
  Music,            // Music, Entertainment
  Shirt,            // Clothing, Fashion
  Building,         // Construction, Architecture
  Plane,            // Travel, Tourism
  Smartphone,       // Technology, Electronics
  Flower,           // Florist, Garden
  Gift,             // Events, Gifts
  LucideIcon
} from 'lucide-react';

// Category icon mapping
export const categoryIcons: Record<string, LucideIcon> = {
  // Food & Dining
  'Restaurant': Utensils,
  'Food & Dining': Utensils,
  'Bakery': Utensils,
  'Catering': Utensils,
  'Coffee Shop': Coffee,
  'Bar': Coffee,
  'Food Truck': Truck,
  
  // Retail & Shopping
  'Retail': ShoppingBag,
  'Shopping': ShoppingBag,
  'Grocery': ShoppingBag,
  'Clothing': Shirt,
  'Fashion': Shirt,
  'Boutique': Shirt,
  'Bookstore': GraduationCap,
  
  // Beauty & Personal Care
  'Beauty': Scissors,
  'Hair Salon': Scissors,
  'Barber Shop': Scissors,
  'Spa': Heart,
  'Nail Salon': Scissors,
  'Skincare': Heart,
  
  // Health & Wellness
  'Healthcare': Heart,
  'Medical': Heart,
  'Dental': Heart,
  'Fitness': Heart,
  'Gym': Heart,
  'Wellness': Heart,
  'Therapy': Heart,
  
  // Professional Services
  'Legal': Briefcase,
  'Accounting': Briefcase,
  'Consulting': Briefcase,
  'Real Estate': Home,
  'Insurance': Briefcase,
  'Financial Services': Briefcase,
  
  // Home & Automotive
  'Auto Repair': Wrench,
  'Auto Services': Wrench,
  'Home Repair': Wrench,
  'Plumbing': Wrench,
  'Electrical': Wrench,
  'HVAC': Wrench,
  'Cleaning': Home,
  'Landscaping': Flower,
  
  // Arts & Entertainment
  'Arts & Crafts': Palette,
  'Entertainment': Music,
  'Photography': Camera,
  'Music': Music,
  'Dance': Music,
  'Theater': Music,
  'Gallery': Palette,
  
  // Education & Training
  'Education': GraduationCap,
  'Training': GraduationCap,
  'Tutoring': GraduationCap,
  'School': GraduationCap,
  
  // Technology
  'Technology': Smartphone,
  'IT Services': Smartphone,
  'Web Design': Smartphone,
  'Electronics': Smartphone,
  
  // Travel & Transportation
  'Travel': Plane,
  'Transportation': Truck,
  'Tourism': Plane,
  'Hotel': Building,
  
  // Events & Special Services
  'Event Planning': Gift,
  'Wedding Services': Gift,
  'Florist': Flower,
  'Party Supplies': Gift,
  
  // Community & Non-Profit
  'Community': Users,
  'Non-Profit': Users,
  'Religious': Users,
  'Social Services': Users,
};

// Default icon for unknown categories
export const defaultCategoryIcon = Building;

export function getCategoryIcon(category: string): LucideIcon {
  return categoryIcons[category] || defaultCategoryIcon;
}
