import { RefreshCw } from 'lucide-react';

interface PullToRefreshIndicatorProps {
  pullDistance: number;
  isRefreshing: boolean;
  threshold?: number;
}

export function PullToRefreshIndicator({ 
  pullDistance, 
  isRefreshing, 
  threshold = 80 
}: PullToRefreshIndicatorProps) {
  const progress = Math.min(pullDistance / threshold, 1);
  const opacity = Math.max(0.3, progress);
  const scale = 0.5 + (progress * 0.5);

  if (pullDistance === 0 && !isRefreshing) return null;

  return (
    <div 
      className="flex justify-center items-center py-4 transition-all duration-200"
      style={{ 
        transform: `translateY(${Math.min(pullDistance * 0.5, threshold * 0.5)}px)`,
        opacity 
      }}
    >
      <div 
        className={`flex flex-col items-center gap-2 ${
          isRefreshing ? 'animate-pulse' : ''
        }`}
        style={{ transform: `scale(${scale})` }}
      >
        <div className={`${isRefreshing ? 'animate-spin' : ''}`}>
          <RefreshCw 
            size={24} 
            className={`${
              progress >= 1 || isRefreshing 
                ? 'text-green-600' 
                : 'text-gray-400'
            }`} 
          />
        </div>
        <p className="text-xs text-gray-500">
          {isRefreshing 
            ? 'Refreshing...' 
            : progress >= 1 
              ? 'Release to refresh' 
              : 'Pull to refresh'
          }
        </p>
      </div>
    </div>
  );
}
