import { useEffect, useMemo, useState, useRef } from 'react';
import seed from '../data/dfw-businesses-enhanced.json';
import { supabase } from '../lib/supabase';
import type { Business } from '../types';
import { BusinessCard } from '../components/BusinessCard';
import { BusinessModal } from '../components/BusinessModal';
import { SearchBar } from '../components/SearchBar';
import { AutocompleteSearch } from '../components/AutocompleteSearch';
import { LoadingGrid } from '../components/LoadingSkeleton';
import { useDebounce } from '../hooks/useDebounce';
import { usePullToRefresh } from '../hooks/usePullToRefresh';
import { PullToRefreshIndicator } from '../components/PullToRefreshIndicator';
import { Filters } from '../components/Filters';
import { LocationFilter } from '../components/LocationFilter';
import { PushNotificationSettings } from '../components/PushNotificationSettings';
import { addToRecentlyViewed, getRecentlyViewed } from '../lib/localStorage';
import { calculateDistance } from '../hooks/useGeolocation';
import { detectBusinessChanges } from '../lib/businessChangeTracker';
import { startBusinessMonitoring } from '../lib/pushNotifications';

type SortOption = 'name' | 'rating' | 'reviewCount' | 'category' | 'distance';

export default function Directory() {
  const [list, setList] = useState<Business[]>(seed as Business[]);
  const [category, setCategory] = useState('');
  const [city, setCity] = useState('');
  const [q, setQ] = useState('');
  const [selectedBusiness, setSelectedBusiness] = useState<Business | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [sortBy, setSortBy] = useState<SortOption>('name');
  const [recentlyViewed, setRecentlyViewed] = useState<string[]>([]);
  const [showRecentlyViewed, setShowRecentlyViewed] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [userLocation, setUserLocation] = useState<{ latitude: number; longitude: number } | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const dq = useDebounce(q, 200);

  // Function to refresh data
  const refreshData = async () => {
    setIsLoading(true);
    
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
    const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
    
    let newBusinesses = [...seed as Business[]];
    
    if (supabaseUrl && supabaseKey && supabaseUrl !== 'your-supabase-url-here') {
      try {
        const { data, error } = await supabase.from('businesses').select('*').eq('status','approved');
        if (!error && data) {
          newBusinesses = mergeUnique([...seed as Business[]], data);
        }
      } catch (err) {
        console.warn('Supabase connection failed during refresh:', err);
      }
    }
    
    // Check for business changes
    try {
      detectBusinessChanges(newBusinesses);
    } catch (err) {
      console.warn('Business change detection failed:', err);
    }
    
    setList(newBusinesses);
    
    // Simulate minimum refresh time for better UX
    await new Promise(resolve => setTimeout(resolve, 500));
    setIsLoading(false);
  };

  // Pull-to-refresh hook with more restrictive threshold
  const { isPulling, isRefreshing, pullDistance, bindToElement } = usePullToRefresh({
    onRefresh: refreshData,
    threshold: 120 // Increased threshold to prevent accidental triggers
  });

  // Bind pull-to-refresh to container
  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      return bindToElement(container);
    }
  }, [bindToElement]);

  useEffect(() => {
    // Initial load
    refreshData();
    setRecentlyViewed(getRecentlyViewed());
    
    // Start business monitoring for push notifications
    startBusinessMonitoring();
  }, []);
  
  const categories = useMemo(() => Array.from(new Set(list.map(b => b.category))).sort(), [list]);
  const cities = useMemo(() => Array.from(new Set(list.map(b => b.city))).sort(), [list]);

  const filtered = useMemo(() => {
    let businesses = list.filter(b => {
      const matchCategory = category ? b.category === category : true;
      const matchCity = city ? b.city === city : true;
      const search = dq.toLowerCase();
      const matchQ = !search || [b.name, b.category, b.city, b.description ?? ''].some(x => x.toLowerCase().includes(search));
      return matchCategory && matchCity && matchQ;
    });

    // Calculate distances if user location is available
    if (userLocation) {
      businesses = businesses.map(business => {
        let distance: number | undefined;
        
        // Calculate distance if business has coordinates
        if (business.coordinates) {
          distance = calculateDistance(
            userLocation.latitude,
            userLocation.longitude,
            business.coordinates.latitude,
            business.coordinates.longitude
          );
        } else if (business.city) {
          // Fallback: estimate distance based on city (rough estimates for DFW area)
          const cityDistances: Record<string, number> = {
            'Dallas': 15,
            'Fort Worth': 25,
            'Arlington': 20,
            'Plano': 30,
            'Irving': 18,
            'Garland': 22,
            'Grand Prairie': 24,
            'Mesquite': 25,
            'Carrollton': 28,
            'Richardson': 25,
            'Denton': 35,
            'McKinney': 40,
            'Frisco': 35,
            'Lewisville': 30
          };
          distance = cityDistances[business.city] || 30; // Default 30 miles
        }
        
        return {
          ...business,
          distance
        };
      });
    }

    return businesses;
  }, [list, category, city, dq, userLocation]);

  const sorted = useMemo(() => {
    return [...filtered].sort((a, b) => {
      switch (sortBy) {
        case 'distance':
          if (a.distance && b.distance) {
            return a.distance - b.distance;
          }
          return 0;
        case 'rating':
          return (b.rating || 0) - (a.rating || 0);
        case 'reviewCount':
          return (b.reviewCount || 0) - (a.reviewCount || 0);
        case 'category':
          return a.category.localeCompare(b.category);
        case 'name':
        default:
          return a.name.localeCompare(b.name);
      }
    });
  }, [filtered, sortBy]);

  const recentlyViewedBusinesses = useMemo(() => {
    return recentlyViewed.map(name => list.find(b => b.name === name)).filter(Boolean) as Business[];
  }, [recentlyViewed, list]);

  const handleBusinessClick = (business: Business) => {
    addToRecentlyViewed(business.name);
    setRecentlyViewed(getRecentlyViewed());
    setSelectedBusiness(business);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedBusiness(null);
  };

  return (
    <div ref={containerRef} className="p-4">
      {/* Pull to Refresh Indicator */}
      <PullToRefreshIndicator 
        pullDistance={pullDistance}
        isRefreshing={isRefreshing}
      />
      
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">DFW Black Business Hub</h1>
        <p className="text-gray-600 dark:text-gray-300">Discover and support Black-owned businesses in the Dallas-Fort Worth area</p>
      </div>
      
      {/* Enhanced Search Bar with Autocomplete */}
      <div className="mb-4">
        <AutocompleteSearch
          value={q}
          onChange={setQ}
          businesses={list}
          placeholder="Search businesses, categories, or cities..."
          onBusinessSelect={handleBusinessClick}
        />
      </div>
      
      {/* Filters and Sort */}
      <div className="mb-4 space-y-3">
        <Filters
          categories={categories} cities={cities}
          selectedCategory={category} selectedCity={city}
          onCategory={setCategory} onCity={setCity}
        />
        
        {/* Location and Push Notification Settings */}
        <div className="flex flex-col sm:flex-row justify-between items-center gap-3">
          <LocationFilter
            onLocationSet={(lat, lng) => setUserLocation({ latitude: lat, longitude: lng })}
            onLocationClear={() => setUserLocation(null)}
            hasLocation={!!userLocation}
            isLoading={isLoading}
          />
          
          <PushNotificationSettings />
        </div>
        
        {/* Sort Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Sort by:</label>
            <select 
              className="px-3 py-1 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded text-sm focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
              value={sortBy}
              onChange={e => setSortBy(e.target.value as SortOption)}
            >
              <option value="name">Name</option>
              <option value="rating">Rating</option>
              <option value="reviewCount">Most Reviews</option>
              <option value="category">Category</option>
              {userLocation && <option value="distance">Distance</option>}
            </select>
          </div>
          
          {/* Recently Viewed Toggle */}
          {recentlyViewed.length > 0 && (
            <button
              onClick={() => setShowRecentlyViewed(!showRecentlyViewed)}
              className="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1"
            >
              <span>📖</span>
              Recently Viewed ({recentlyViewed.length})
            </button>
          )}
        </div>
      </div>

      {/* Recently Viewed Section */}
      {showRecentlyViewed && recentlyViewedBusinesses.length > 0 && (
        <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border dark:border-gray-700">
          <h2 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
            <span>📖</span>
            Recently Viewed
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {recentlyViewedBusinesses.slice(0, 4).map((b, idx) => (
              <BusinessCard 
                key={b.name + idx} 
                b={b} 
                onClick={() => handleBusinessClick(b)}
              />
            ))}
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoading ? (
        <div>
          <div className="mb-4 flex items-center gap-2 text-gray-600">
            <div className="animate-spin w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full"></div>
            Loading businesses...
          </div>
          <LoadingGrid count={6} />
        </div>
      ) : (
        <>
          {/* Business Directory */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mt-4 overflow-hidden">
            {sorted.map((b, idx) => (
              <BusinessCard 
                key={b.name + idx} 
                b={b} 
                onClick={() => handleBusinessClick(b)}
              />
            ))}
          </div>
          
          {sorted.length === 0 && (
            <div className="mt-8 text-center py-8">
              <div className="text-gray-400 text-6xl mb-4">🔍</div>
              <p className="text-gray-500 mb-4 text-lg">No matches found.</p>
              <button 
                onClick={() => {
                  setCategory('');
                  setCity('');
                  setQ('');
                }}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Clear all filters
              </button>
            </div>
          )}
          
          {/* Results Summary */}
          {sorted.length > 0 && (
            <div className="mt-6 text-center text-sm text-gray-600 border-t pt-4">
              Showing {sorted.length} of {list.length} businesses
            </div>
          )}
        </>
      )}
      
      <BusinessModal
        business={selectedBusiness}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
      </div>
  );
}
function mergeUnique(a: Business[], b: Business[]) {
  const names = new Set(a.map(x => x.name));
  return [...a, ...b.filter(x => !names.has(x.name))];
}

