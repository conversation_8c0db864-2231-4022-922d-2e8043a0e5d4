{"root": ["./src/app.tsx", "./src/main.tsx", "./src/pwa.ts", "./src/sw.ts", "./src/types.ts", "./src/components/autocompletesearch.tsx", "./src/components/bottomnavigation.tsx", "./src/components/businesscard.tsx", "./src/components/businessmodal.tsx", "./src/components/businessmodalnew.tsx", "./src/components/favoritebutton.tsx", "./src/components/filters.tsx", "./src/components/installpwa.tsx", "./src/components/loadingskeleton.tsx", "./src/components/locationfilter.tsx", "./src/components/pulltorefreshindicator.tsx", "./src/components/pushnotificationsettings.tsx", "./src/components/reportissuemodal.tsx", "./src/components/reviews.tsx", "./src/components/searchbar.tsx", "./src/components/themetoggle.tsx", "./src/hooks/usedebounce.ts", "./src/hooks/usefavorites.ts", "./src/hooks/usegeolocation.ts", "./src/hooks/usenotifications.tsx", "./src/hooks/usepulltorefresh.ts", "./src/hooks/usetheme.tsx", "./src/lib/businesschangetracker.ts", "./src/lib/businessutils.ts", "./src/lib/categoryicons.ts", "./src/lib/localstorage.ts", "./src/lib/pushnotifications.ts", "./src/lib/supabase.ts", "./src/pages/directory.tsx", "./src/pages/favorites.tsx", "./src/pages/submit.tsx", "./vite.config.ts"], "version": "5.9.2"}